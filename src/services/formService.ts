import axios from 'axios';

interface FormData {
  name: string;
  surname: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  town: string;
  gender: string;
  birthday: string;
  isActive?: boolean;
}

interface FormToCampaignData {
  formId: number;
  campaignId: number;
  isActive: boolean;
}

export const formService = {
  // Kullanıcının kampanya için daha önce form gönderip göndermediğini kontrol et
  checkExistingForm: async (email: string, campaignId: number) => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/form-to-campaign');

      if (response.data && Array.isArray(response.data)) {
        const existingForm = response.data.find((formCampaign: any) => {
          const match = formCampaign.campaign?.id === campaignId &&
                       formCampaign.form?.email === email &&
                       formCampaign.isActive;

          return match;
        });

        return !!existingForm;
      }
      return false;
    } catch (error) {
      // API çağrısında hata olursa, mevcut form olmadığını varsayabiliriz.
      // console.error('checkExistingForm API hatası:', error);
      return false;
    }
  },

  // Form kaydetme
  createForm: async (formData: FormData) => {
    // Önce mevcut formları kontrol et
    const response = await axios.get('https://360avantajli.com/api/Campaign_Service/form');
    if (response.data && Array.isArray(response.data)) {
      const existingForm = response.data.find((form: any) =>
        form.email === formData.email && form.isActive
      );

      if (existingForm) {
        return existingForm; // Mevcut formu döndür
      }
    }

    // Eğer form yoksa yeni form oluştur
    const createResponse = await axios.post('https://360avantajli.com/api/Campaign_Service/form', {
      ...formData,
      isActive: true
    });
    return createResponse.data;
  },

  // Form ve kampanya ilişkisi oluşturma
  createFormToCampaign: async (data: FormToCampaignData) => {
    // Önce mevcut ilişkiyi kontrol et
    const response = await axios.get('https://360avantajli.com/api/Campaign_Service/form-to-campaign');
    if (response.data && Array.isArray(response.data)) {
      const existingRelation = response.data.find((relation: any) =>
        relation.form?.id === data.formId &&
        relation.campaign?.id === data.campaignId &&
        relation.isActive
      );

      if (existingRelation) {
        return existingRelation; // Mevcut ilişkiyi döndür
      }
    }

    // Eğer ilişki yoksa yeni ilişki oluştur
    const createResponse = await axios.post('https://360avantajli.com/api/Campaign_Service/form-to-campaign', {
      formId: data.formId,
      campaignId: data.campaignId,
      isActive: true
    });
    return createResponse.data;
  },

  // Form ve kampanya işlemlerini sırayla yapma
  submitFormAndCreateCampaignRelation: async (formData: FormData, campaignId: number) => {
    // Önce kullanıcının bu kampanya için daha önce form gönderip göndermediğini kontrol et
    const hasExistingForm = await formService.checkExistingForm(formData.email, campaignId);
    if (hasExistingForm) {
      throw new Error('Bu kampanya için daha önce teklif aldınız. Başka bir kampanya seçebilirsiniz.');
    }

    // Önce formu kaydet veya mevcut formu al
    const formResponse = await formService.createForm(formData);

    // Form kaydedildikten sonra kampanya ilişkisini oluştur
    const formToCampaignResponse = await formService.createFormToCampaign({
      formId: formResponse.id,
      campaignId: campaignId,
      isActive: true
    });

    return {
      form: formResponse,
      formToCampaign: formToCampaignResponse
    };
  }
};