import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useTestMode } from "../components/common/TestModeWrapper";
import { createSlug as slugifyTurkish } from "../utils/urlUtils";
import {
  Container,
  Box,
  Typography,
  Button,
  Snackbar,
  Alert,
  useTheme,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Breadcrumbs,
  Link as MuiLink,
  Tabs,
  Tab,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import {
  NavigateNext as NavigateNextIcon,
  Close as CloseIcon,
  RemoveRedEye as ViewIcon,
  People as PeopleIcon,
  Share as ShareIcon,
  WhatsApp as WhatsAppIcon,
  MailOutline as MailIcon,
  ContentCopy as ContentCopyIcon,
  Instagram as InstagramIcon,
  Twitter as TwitterIcon,
} from "@mui/icons-material";
import axios from "axios";
import { useCategories } from "../contexts/CategoryContext";
import { useIntl, FormattedMessage } from "react-intl";
import QuoteForm from "../components/features/quote/QuoteForm";
import { alpha } from "@mui/material/styles";
import { getTimeRemaining, formatDateWithTime } from "../utils/dateUtils";
import useApiCache from "../hooks/useApiCache";
import { Link as RouterLink } from "react-router-dom";
import { getOrCreateUniqueId } from "../utils/cookieConsentUtils";
import { formService } from "../services/formService";
import { useAuth } from "../contexts/AuthContext";

interface CampaignDetailField {
  type: "text" | "radio" | "checkbox";
  label: string;
  value: string | boolean | string[];
  options?: string[];
  required?: boolean;
}

interface CampaignData {
  id: string;
  title: string;
  name: string;
  brand: string;
  description: string;
  descriptionTitle?: string;
  imageUrl: string;
  logoUrl?: string;
  endDate: string;
  discount: string;
  category: string;
  brandId?: string;
  brandUrl?: string;
  isActive: boolean;
  features?: Record<string, unknown>;
  stats?: Record<string, unknown>;
  conditions?: string[];
  details?: Record<string, CampaignDetailField>;
  campaignUrl?: string | null;
  [key: string]: unknown;
}

// Görsel tipi
interface DisplayImage {
  isShowcase: boolean;
  url: string;
}

const CampaignDetail: React.FC = () => {
  const { campaignName: campaignSlugFromUrl } = useParams<{ campaignName: string }>();
  const standardNavigate = useNavigate();
  const { navigate: testNavigate, isTestMode } = useTestMode();
  const navigate = isTestMode ? testNavigate : standardNavigate;

  const [quoteFormOpen, setQuoteFormOpen] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [showShareAlert, setShowShareAlert] = useState(false);
  const [showEndedMessage, setShowEndedMessage] = useState(false);
  const [hasExistingForm, setHasExistingForm] = useState(false);
  const [checkingExistingForm, setCheckingExistingForm] = useState(false);
  const [shouldOpenFormInitially, setShouldOpenFormInitially] = useState(false);

  // State for share dialog
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);

  // New state for Instagram-specific Snackbar
  const [showInstagramInfoSnackbar, setShowInstagramInfoSnackbar] = useState(false);
  const [instagramSnackbarMessage, setInstagramSnackbarMessage] = useState("");

  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === "dark";

  const { flattenedCategories, getCategoryById, getCategoryPath } =
    useCategories();
  const { user, isAuthenticated } = useAuth();

  const [campaign, setCampaign] = useState<CampaignData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [relatedCampaigns, setRelatedCampaigns] = useState<CampaignData[]>([]);
  const [loadingRelated, setLoadingRelated] = useState(false);

  const [campaignImages, setCampaignImages] = useState<DisplayImage[]>([]);

  const [openImageModal, setOpenImageModal] = useState(false);
  const [modalImageUrl, setModalImageUrl] = useState<string | null>(null);
  const [currentDetailIndex, setCurrentDetailIndex] = useState(0);
  const [isShowcaseModal, setIsShowcaseModal] = useState(false);

  const [detailsTab, setDetailsTab] = useState(0);
  const handleDetailsTabChange = (
    _event: React.SyntheticEvent,
    newValue: number
  ) => {
    setDetailsTab(newValue);
  };

  // brand-to-campaign datasını çek
  const { data: brandCampaignData } = useApiCache<Record<string, unknown>[]>(
    "https://360avantajli.com/api/Campaign_Service/brand-to-campaign",
    undefined,
    5 * 60 * 1000 // 5 dakika cache süresi
  );

  // Görüntülenme ve ilgilenen kullanıcı sayısı için state
  const [views24h, setViews24h] = useState<number>(0);
  const [interested24h, setInterested24h] = useState<number>(0);

  // Modal açıldığında görselin yüklenmesini sağlamak için, görselin src özelliğini kontrol et
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  useEffect(() => {
    const fetchCampaign = async () => {
      try {
        setLoading(true);
        const campaignResponse = await axios.get(
          "https://360avantajli.com/api/Campaign_Service/campaign"
        );

        if (campaignResponse.data && Array.isArray(campaignResponse.data)) {
          type ApiCampaignType = { id: string | number; name: string; title?: string; isActive?: boolean; category?: any; brand?: any; description?: string; imageUrl?: string; logoUrl?: string; brandUrl?: string; brandId?: string; endDate?: string; discount?: string; conditions?: string[]; price?: string; monthlyPayment?: string; term?: string; downPayment?: string; interestRate?: string; details?: any; campaignUrl?: string | null; };

          let foundCampaign: ApiCampaignType | undefined = undefined;

          if (campaignSlugFromUrl) {
            const slugFromUrl = slugifyTurkish(campaignSlugFromUrl);
            foundCampaign = (campaignResponse.data as ApiCampaignType[]).find(
              (c) => c.name && slugifyTurkish(c.name) === slugFromUrl && c.isActive === true
            );
          }

          if (foundCampaign) {
            const expectedSlug = slugifyTurkish(foundCampaign.name);
            const currentSlugInUrl = campaignSlugFromUrl ? slugifyTurkish(campaignSlugFromUrl) : null;

            if (currentSlugInUrl !== expectedSlug) {
              const newPath = isTestMode
                ? `/test/campaigns/${expectedSlug}`
                : `/campaigns/${expectedSlug}`;
              navigate(newPath, { replace: true });
              return; 
            }

            const campaignIdString = foundCampaign.id.toString();

            const brandCampaignResponse = await axios.get(
              "https://360avantajli.com/api/Campaign_Service/brand-to-campaign"
            );

            let brandInfo = {
              name: foundCampaign.brand?.name || "Marka",
              logoUrl: foundCampaign.logoUrl || "",
              brandUrl: foundCampaign.brandUrl || "",
              brandId: foundCampaign.brandId?.toString() || "",
            };

            if (
              brandCampaignResponse.data &&
              Array.isArray(brandCampaignResponse.data)
            ) {
              const brandCampaign = brandCampaignResponse.data.find(
                (bc: {
                  campaign?: { id: string | number };
                  isActive?: boolean;
                  brand?: {
                    name?: string;
                    logo?: string;
                    logoUrl?: string;
                    brandUrl?: string;
                  };
                }) =>
                  bc.campaign?.id === foundCampaign.id && bc.isActive === true
              );

              if (brandCampaign && brandCampaign.brand) {
                brandInfo = {
                  name:
                    brandCampaign.brand.name || foundCampaign.brand || "Marka",
                  logoUrl:
                    brandCampaign.brand.logo ||
                    brandCampaign.brand.logoUrl ||
                    foundCampaign.logoUrl ||
                    "",
                  brandUrl: brandCampaign.brand.brandUrl || "",
                  brandId: brandCampaign.brand.id,
                };

                foundCampaign.brandId = brandCampaign.brand.id;
              }
            }

            let campaignDetails = {};
            try {
              if (
                foundCampaign.details &&
                typeof foundCampaign.details === "object"
              ) {
                campaignDetails = foundCampaign.details;
              } else if (foundCampaign.category?.campaignDetail?.id) {
                const detailResponse = await axios.get(
                  `https://360avantajli.com/api/Campaign_Service/campaign-detail/${foundCampaign.category.campaignDetail.id}`
                );
                if (detailResponse.data && detailResponse.data.details) {
                  campaignDetails = detailResponse.data.details;
                }
              }
            } catch (e) {
              // Detay çekme hatası olursa sessizce devam et, temel kampanya bilgileri yine de gösterilecek
              // Hata mesajı göstermeye gerek yok çünkü bu opsiyonel bir özellik
            }

            // Fetch campaign URL from /api/Campaign_Service/campaign-url
            // Initialize with null, to be overridden if a specific URL is found.
            let specificCampaignUrlData: string | null = null;
            try {
                const campaignUrlsResponse = await axios.get(
                    "https://360avantajli.com/api/Campaign_Service/campaign-url"
                );
                if (campaignUrlsResponse.data && Array.isArray(campaignUrlsResponse.data)) {
                    interface FetchedCampaignUrl {
                        id: number;
                        campaign: { id: number; name: string; };
                        url: string;
                        isActive: boolean;
                    }
                    const activeCampaignUrlEntry = (campaignUrlsResponse.data as FetchedCampaignUrl[]).find(
                        (urlEntry) =>
                            urlEntry.campaign?.id?.toString() === foundCampaign?.id?.toString() &&
                            urlEntry.isActive === true
                    );
                    if (activeCampaignUrlEntry && activeCampaignUrlEntry.url) {
                        specificCampaignUrlData = activeCampaignUrlEntry.url;
                    }
                }
            } catch (urlError) {
                // specificCampaignUrlData remains null on error
            }

            if (foundCampaign) {
                (foundCampaign as any).campaignUrl = specificCampaignUrlData;
            }

            setCampaign({
              id: foundCampaign!.id.toString(),
              title: foundCampaign!.title || foundCampaign!.name,
              name: foundCampaign!.name,
              brand: brandInfo.name,
              description: foundCampaign!.description || "",
              imageUrl: foundCampaign!.imageUrl || "/placeholder-image.jpg",
              logoUrl: brandInfo.logoUrl || "",
              brandUrl: brandInfo.brandUrl || "",
              brandId: brandInfo.brandId,
              endDate: foundCampaign!.endDate || "",
              discount: foundCampaign!.discount
                ? `%${foundCampaign!.discount}`
                : "%0",
              category: foundCampaign!.category?.id?.toString() || "0",
              isActive: foundCampaign!.isActive || true,
              conditions: foundCampaign!.conditions
                ? typeof foundCampaign!.conditions === "string"
                  ? [foundCampaign!.conditions]
                  : Array.isArray(foundCampaign!.conditions)
                  ? foundCampaign!.conditions
                  : []
                : [],
              features: {
                price: foundCampaign!.price || "0 TL",
                monthlyPayment: foundCampaign!.monthlyPayment || "0 TL",
                term: foundCampaign!.term || "0 ay",
                downPayment: foundCampaign!.downPayment || "0 TL",
                interestRate: foundCampaign!.interestRate || "%0",
              },
              details: campaignDetails as Record<string, CampaignDetailField>,
              campaignUrl: foundCampaign!.campaignUrl,
            });
          } else {
            setError("Kampanya bulunamadı veya aktif değil.");
          }
        }
      } catch (_) {
        setError("Kampanya yüklenirken bir hata oluştu.");
      } finally {
        setLoading(false);
      }
    };

    fetchCampaign();
  }, [campaignSlugFromUrl]);

  // Kullanıcının bu kampanya için daha önce form gönderip göndermediğini kontrol et
  useEffect(() => {
    const checkExistingForm = async () => {
      if (!isAuthenticated || !user?.username || !campaign?.id) {
        setHasExistingForm(false);
        return;
      }

      try {
        setCheckingExistingForm(true);

        // Önce kullanıcının email'ini customer API'sinden al
        const customerResponse = await axios.get('https://360avantajli.com/api/Campaign_Service/customer');

        if (!customerResponse.data || !Array.isArray(customerResponse.data)) {
          setHasExistingForm(false);
          return;
        }

        const customerData = customerResponse.data.find((customer: any) => {
          if (customer.username && customer.username === user.username) {
            return true;
          }
          return customer.email === user.username;
        });

        if (!customerData || !customerData.email) {
          setHasExistingForm(false);
          return;
        }

        const hasForm = await formService.checkExistingForm(customerData.email, parseInt(campaign.id));
        setHasExistingForm(hasForm);
      } catch (error) {
        setHasExistingForm(false);
      } finally {
        setCheckingExistingForm(false);
      }
    };

    checkExistingForm();
  }, [isAuthenticated, user?.username, campaign?.id]);

  // Kampanya yüklendiğinde ve form durumu kontrol edildiğinde formu açma mantığı
  useEffect(() => {
    if (campaign && !checkingExistingForm) {
      if (!hasExistingForm) {
        const timer = setTimeout(() => {
          setQuoteFormOpen(true);
        }, 2000);
    
        return () => {
          clearTimeout(timer);
        };
      }
    }
  }, [campaign, checkingExistingForm, hasExistingForm]);

  useEffect(() => {
    if (campaign && campaign.category) {
      const fetchRelatedCampaigns = async () => {
        try {
          setLoadingRelated(true);
          const response = await axios.get(
            "https://360avantajli.com/api/Campaign_Service/campaign"
          );
          if (response.data && Array.isArray(response.data)) {
            // Fetch brand-to-campaign data if not already available
            let brandCampaignResponse;
            if (brandCampaignData) {
              brandCampaignResponse = { data: brandCampaignData };
            } else {
              brandCampaignResponse = await axios.get(
                "https://360avantajli.com/api/Campaign_Service/brand-to-campaign"
              );
            }

            const related = response.data
              .filter(
                (c: { id: string | number; isActive?: boolean; name?: string }) =>
                  c.id.toString() !== campaign.id && c.isActive === true
              )
              .slice(0, 3)
              .map((relatedCampaign: any) => {
                // Get brand information from brand-to-campaign data
                let brandInfo = {
                  name: relatedCampaign.brand || "Marka",
                  logoUrl: relatedCampaign.logoUrl || "",
                  brandUrl: relatedCampaign.brandUrl || "",
                  brandId:
                    relatedCampaign.brandId ||
                    (relatedCampaign.brand && relatedCampaign.brand.id
                      ? relatedCampaign.brand.id
                      : ""),
                };

                if (
                  brandCampaignResponse.data &&
                  Array.isArray(brandCampaignResponse.data)
                ) {
                  const brandCampaign = brandCampaignResponse.data.find(
                    (bc: {
                      campaign?: { id: string | number };
                      isActive?: boolean;
                      brand?: {
                        name?: string;
                        logo?: string;
                        logoUrl?: string;
                        brandUrl?: string;
                        id?: string;
                      };
                    }) =>
                      bc.campaign?.id === relatedCampaign.id && bc.isActive === true
                  );

                  if (brandCampaign && brandCampaign.brand) {
                    brandInfo = {
                      name:
                        brandCampaign.brand.name || relatedCampaign.brand || "Marka",
                      logoUrl:
                        brandCampaign.brand.logo ||
                        brandCampaign.brand.logoUrl ||
                        relatedCampaign.logoUrl ||
                        "",
                      brandUrl: brandCampaign.brand.brandUrl || "",
                      brandId: brandCampaign.brand.id,
                    };
                  }
                }

                return {
                  id: relatedCampaign.id.toString(),
                  title: relatedCampaign.name,
                  name: relatedCampaign.name,
                  brand: brandInfo.name,
                  description: relatedCampaign.description || "",
                  imageUrl: relatedCampaign.imageUrl || "/placeholder-image.jpg",
                  logoUrl: brandInfo.logoUrl,
                  brandUrl: brandInfo.brandUrl,
                  brandId: brandInfo.brandId?.toString(),
                  endDate: relatedCampaign.endDate || "",
                  discount: relatedCampaign.discount ? `%${relatedCampaign.discount}` : "%0",
                  category: relatedCampaign.category?.id.toString() || "0",
                  isActive: relatedCampaign.isActive || true,
                  features: relatedCampaign.features || { price: "0 TL" },
                  details: relatedCampaign.details || {},
                };
              });
            setRelatedCampaigns(related as CampaignData[]);
          }
        } catch (error) {
          console.error("Error fetching related campaigns:", error);
        } finally {
          setLoadingRelated(false);
        }
      };
      fetchRelatedCampaigns();
    }
  }, [campaign, brandCampaignData, getCategoryById, getCategoryPath]);

  // Vitrin görselini alttaki küçük fotoğrafların başına ekle
  const showcaseImage = campaignImages.find((img) => img.isShowcase);
  const detailImages = campaignImages.filter((img) => !img.isShowcase);
  const galleryImages = showcaseImage
    ? [showcaseImage, ...detailImages]
    : detailImages;

  // useEffect ve useState hook'larını her zaman aynı sırada çağır
  useEffect(() => {
    if (showcaseImage) {
      setCurrentDetailIndex(0);
    }
  }, [showcaseImage]);

  // Fotoğrafa tıklandığında tam ekranda incelenebilir olması için modal ekle
  const [modalOpen, setModalOpen] = useState(false);

  const handleOpenModal = () => {
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
  };

  // Handle apply button click
  const handleApplyClick = () => {
    // Eğer kullanıcı bu kampanya için daha önce form göndermiş ise, formu açma
    if (hasExistingForm) {
      return;
    }

    if (campaign?.endDate) {
      // DD-MM-YYYY HH:mm:ss formatını parse et
      const [datePart, timePart] = campaign.endDate.split(' ');
      const [day, month, year] = datePart.split('-');
      const [hours, minutes, seconds] = timePart.split(':');

      // Yeni Date objesi oluştur (ay 0'dan başladığı için -1 yapıyoruz)
      const endDate = new Date(
        parseInt(year),
        parseInt(month) - 1,
        parseInt(day),
        parseInt(hours),
        parseInt(minutes),
        parseInt(seconds)
      );

      const now = new Date();

      if (endDate < now) {
        setShowEndedMessage(true); // Kampanya bitmişse mesajı göster
        return;
      }
    }
    setQuoteFormOpen(true); // Kampanya bitmemişse teklif formunu aç
  };
  useEffect(() => {
    if (campaign) {
      const fetchCampaignImages = async () => {
        // Vitrin görseli için state
        setCampaignImages([]); // Sıfırla
        // Vitrin görseli
        const showcase = {
          isShowcase: true,
          url: getShowcaseImageUrl(campaign.id),
        };
        // Detay görselleri
        const detailUrls = await getDetailImageUrls(campaign.id);
        const details = detailUrls.map((url) => ({ isShowcase: false, url }));
        setCampaignImages([showcase, ...details]);
      };
      fetchCampaignImages();
    }
  }, [campaign]);

  const getBrandLogoUrl = (brandId: string, fallbackLogoUrl?: string) =>
    brandId
      ? `https://360avantajli.com/api/Campaign_Service/brand/${brandId}/image`
      : fallbackLogoUrl || "/placeholder-logo.jpg";

  const renderLogo = (
    url?: string,
    altText: string = "Logo",
    brandId?: string
  ) => {
    // Logo yoksa varsayılan logo göster
    if (!url && !brandId) {
      return (
        <img
          src="/placeholder-logo.jpg"
          alt={altText}
          style={{
            maxWidth: "100%",
            maxHeight: "100%",
            objectFit: "contain",
          }}
        />
      );
    }

    // Formata göre uygun logo URL'sini oluştur
    let imageUrl = url;

    // Eğer brandId varsa yeni endpoint'i kullan
    if (brandId) {
      imageUrl = getBrandLogoUrl(brandId, url);
    }
    // Eğer tam dosya yolu varsa, sadece dosya adını al
    else if (url?.includes("/")) {
      imageUrl = getBrandLogoUrl(url.split("/").pop() || "", url);
    }
    // JPEG için base64 data ekleme
    else if (url?.startsWith("/9j/")) {
      imageUrl = `data:image/jpeg;base64,${url}`;
    }
    // PNG için base64 data ekleme
    else if (
      url?.startsWith("iVBOR") ||
      url?.startsWith("iVBO") ||
      url?.includes("PNG")
    ) {
      imageUrl = `data:image/png;base64,${url}`;
    }

    return (
      <img
        src={imageUrl}
        alt={altText}
        style={{
          maxWidth: "100%",
          maxHeight: "100%",
          objectFit: "contain",
        }}
        onError={(e) => {
          const target = e.target as HTMLImageElement;
          target.onerror = null;
          target.src = "/placeholder-logo.jpg";
        }}
      />
    );
  };

  const handleCloseImageModal = () => {
    setOpenImageModal(false);
    setModalImageUrl(null);
  };

  const handleOpenShowcaseModal = () => {
    setIsShowcaseModal(true);
  };

  const handleCloseShowcaseModal = () => {
    setIsShowcaseModal(false);
  };

  // Her kampanya için deterministik olarak istatistik değerleri üret
  useEffect(() => {
    if (!campaign) return;

    // Kampanya ID'sini sayıya çevir (string ID'ler için)
    const generateNumberFromId = (id: string): number => {
      // ID'yi sayıya çevir, eğer sayı değilse hash oluştur
      if (!isNaN(Number(id))) {
        return Number(id);
      }

      // String ID için basit bir hash fonksiyonu
      let hash = 0;
      for (let i = 0; i < id.length; i++) {
        hash = (hash << 5) - hash + id.charCodeAt(i);
        hash |= 0; // 32-bit integer'a çevir
      }
      return Math.abs(hash);
    };

    // Kampanya ID'sinden deterministik değerler üret (her kullanıcı için aynı olacak)
    const campaignIdNumber = generateNumberFromId(campaign.id);

    // Kampanya ID'sine göre sabit değerler üret (her kullanıcı için aynı olacak)
    // Görüntülenme sayısı: 1000-10000 arası
    const baseViews = 1000 + (campaignIdNumber % 9000);
    // İlgilenen kullanıcı sayısı: 100-1000 arası ve görüntülenme sayısının %5-15'i
    const interestedPercentage = 5 + (campaignIdNumber % 10);
    const baseInterested = Math.floor(baseViews * (interestedPercentage / 100));

    // Günlük küçük varyasyonlar ekle (tarih bazlı, herkes için aynı)
    const today = new Date();
    const dayOfYear = Math.floor(
      (today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) /
        86400000
    );

    // Günlük varyasyon: ±%5
    const dailyVariation = 0.95 + ((dayOfYear + campaignIdNumber) % 10) / 100;

    // Final değerleri hesapla ve yuvarla
    const finalViews = Math.floor(baseViews * dailyVariation);
    const finalInterested = Math.floor(baseInterested * dailyVariation);

    setViews24h(finalViews);
    setInterested24h(finalInterested);
  }, [campaign]);

  useEffect(() => {
    if (campaign && campaign.id && campaign.brandId) {
      const userId = getOrCreateUniqueId();
      const payload = {
        userId,
        targetId: Number(campaign.id),
        targetType: "CAMPAIGN",
        details: "Kullanıcı kampanyaya tıkladı",
        brandId: Number(campaign.brandId),
        campaignId: Number(campaign.id),
      };
      axios
        .post("https://360avantajli.com/api/Campaign_Service/events", payload)
        .catch((err) => {
          // Hata olursa sessizce yut
        });
    }
  }, [campaign]);

  if (loading) {
    return (
      <Container>
        <Box
          sx={{
            py: 8,
            textAlign: "center",
            minHeight: "60vh",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 3 }}>
            Kampanya yükleniyor...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error || !campaign) {
    return (
      <Container>
        <Box
          sx={{
            py: 8,
            textAlign: "center",
            minHeight: "60vh",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            background: isDarkMode
              ? "linear-gradient(145deg, rgba(18,18,18,0.9) 0%, rgba(18,18,18,0.6) 100%)"
              : "linear-gradient(145deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.6) 100%)",
            backdropFilter: "blur(10px)",
            borderRadius: 4,
            transition: "all 0.3s ease-in-out",
          }}
        >
          <Typography
            variant="h4"
            sx={{
              mb: 2,
              fontWeight: 600,
              color: isDarkMode ? "grey.300" : "grey.800",
              textShadow: isDarkMode ? "0 2px 4px rgba(0,0,0,0.2)" : "none",
            }}
          >
            {error || "Kampanya bulunamadı"}
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate("/categories")}
            sx={{
              py: 1.5,
              px: 4,
              borderRadius: 2,
              textTransform: "none",
              fontWeight: 600,
              background: theme.palette.primary.main,
              transition: "all 0.3s ease",
              "&:hover": {
                transform: "translateY(-2px)",
                boxShadow: "0 6px 20px rgba(0,0,0,0.15)",
              },
            }}
          >
            Tüm Kampanyalara Dön
          </Button>
        </Box>
      </Container>
    );
  }

  const handleQuoteFormClose = () => {
    setQuoteFormOpen(false);
  };

  // Share Dialog Handlers
  const handleOpenShareDialog = () => {
    setIsShareDialogOpen(true);
  };

  const handleCloseShareDialog = () => {
    setIsShareDialogOpen(false);
  };

  const handleGenericShare = async () => {
    handleCloseShareDialog();
    const shareUrl = window.location.href;
    const shareTitle = campaign?.name || intl.formatMessage({ id: 'campaignDetails.share.defaultTitle', defaultMessage: "Check out this campaign!" });
    const shareText = campaign?.description || intl.formatMessage({ id: 'campaignDetails.share.defaultDescription', defaultMessage: "Interesting campaign details." });

    if (navigator.share) {
      try {
        await navigator.share({
          title: shareTitle,
          text: shareText,
          url: shareUrl,
        });
      } catch (error) {
        // User cancelled share or error occurred, do nothing.
      }
    }
  };

  const shareViaWhatsApp = () => {
    handleCloseShareDialog();
    const shareUrl = window.location.href;
    const message = `${campaign?.name || intl.formatMessage({ id: 'campaignDetails.share.campaignDefaultName', defaultMessage: "Kampanya" })} - ${shareUrl}`;

    // Basic mobile detection
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    let whatsappUrl = "";
    if (isMobile) {
      whatsappUrl = `whatsapp://send?text=${encodeURIComponent(message)}`;
    } else {
      whatsappUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(message)}`;
    }

    window.open(whatsappUrl, '_blank');
  };

  const shareViaEmail = () => {
    handleCloseShareDialog();
    const shareUrl = window.location.href;
    const subject = campaign?.name || intl.formatMessage({ id: 'campaignDetails.share.defaultTitle', defaultMessage: "Check out this campaign!" });
    const body = intl.formatMessage({
      id: 'campaignDetails.share.emailBody',
      defaultMessage: 'Hi,\n\nCheck out this campaign: {campaignName}\n{campaignDescription}\n\n{campaignUrl}'
    }, {
      campaignName: campaign?.name || '',
      campaignDescription: campaign?.description || '',
      campaignUrl: shareUrl
    });
    window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  };

  const copyLinkToClipboard = () => {
    handleCloseShareDialog();
    navigator.clipboard.writeText(window.location.href);
    setShowShareAlert(true);
  };

  const shareViaInstagram = () => {
    handleCloseShareDialog();
    const shareUrl = window.location.href;
    const textToCopy = `${campaign?.name || intl.formatMessage({ id: 'campaignDetails.share.campaignDefaultName', defaultMessage: "Kampanya" })}: ${shareUrl}`;
    navigator.clipboard.writeText(textToCopy);
    
    // Use custom Snackbar for Instagram instructions
    setInstagramSnackbarMessage(intl.formatMessage({ 
      id: 'campaignDetails.share.instagramInfoCopied',
      defaultMessage: 'Kampanya detayları kopyalandı! Instagram\'ı açıp DM ile yapıştırın.'
    }));
    setShowInstagramInfoSnackbar(true);
    window.open('https://www.instagram.com/direct/inbox/', '_blank');
  };

  const shareViaTwitterDM = () => {
    handleCloseShareDialog();
    const shareUrl = window.location.href;
    const text = intl.formatMessage(
      { id: 'campaignDetails.share.twitterDMText', defaultMessage: 'Check out this campaign: {campaignName} - {campaignUrl}' },
      { campaignName: campaign?.name || '', campaignUrl: shareUrl }
    );
    window.open(`https://twitter.com/messages/compose?text=${encodeURIComponent(text)}`, '_blank');
  };

  const shareAsTweet = () => {
    handleCloseShareDialog();
    const shareUrl = window.location.href;
    const text = intl.formatMessage(
      { id: 'campaignDetails.share.tweetText', defaultMessage: 'Check out this awesome campaign: {campaignName}' },
      { campaignName: campaign?.name || '' }
    );
    // You can add hashtags here if desired, e.g., &hashtags=campaign,promo
    window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(text)}`, '_blank');
  };

  // Vitrin görseli için yeni endpoint
  const getShowcaseImageUrl = (campaignId: string) => {
    if (!campaignId) return "";
    return `https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${campaignId}`;
  };

  // Detay görselleri için yeni endpoint ve /api ekleme
  const getDetailImageUrls = async (campaignId: string): Promise<string[]> => {
    if (!campaignId) return [];
    try {
      const response = await axios.get(
        `https://360avantajli.com/api/Campaign_Service/campaign-image/details/${campaignId}`
      );
      if (typeof response.data === "string") {
        const parser = new DOMParser();
        const doc = parser.parseFromString(response.data, "text/html");
        const images = Array.from(doc.getElementsByTagName("img"));
        return images.map((img) =>
          img.src.replace(
            "https://360avantajli.com/Image_Service",
            "https://360avantajli.com/api/Image_Service"
          )
        );
      }
    } catch (e) {
      console.error("Detay görselleri alınırken hata:", e);
    }
    return [];
  };

  // Breadcrumb verisi
  const breadcrumbs = [
    { label: "Anasayfa", to: isTestMode ? "/test" : "/" },
    { label: "Kategoriler", to: isTestMode ? "/test/categories" : "/categories" },
    ...(campaign?.category
      ? getCategoryPath(parseInt(campaign.category)).map((cat) => ({
          label: cat.name,
          to: isTestMode ? `/test/categories/${slugifyTurkish(cat.name)}` : `/categories/${slugifyTurkish(cat.name)}`,
        }))
      : []),
    ...(campaign?.brand
      ? [{ label: campaign.brand, to: isTestMode ? `/test/brands/${slugifyTurkish(campaign.brand)}` : `/brands/${slugifyTurkish(campaign.brand)}` }]
      : []),
    { label: campaign?.name || "", to: isTestMode ? `/test/campaigns/${slugifyTurkish(campaign?.name || "")}` : `/campaigns/${slugifyTurkish(campaign?.name || "")}` },
  ];

  const categoryName =
    getCategoryById?.(Number(campaign?.category))?.name || "";

  return (
    <Container maxWidth="lg">
      {/* Sayfanın en üstü: Breadcrumb */}
      <Box sx={{ mb: 3, mt: 1 }}>
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          aria-label="breadcrumb"
          sx={{
            "& .MuiBreadcrumbs-separator": {
              mx: 1,
            },
          }}
        >
          {breadcrumbs.map((crumb, idx) =>
            idx < breadcrumbs.length - 1 ? (
              <MuiLink
                key={crumb.to}
                component={RouterLink}
                to={crumb.to}
                underline="hover"
                sx={{
                  color: "text.secondary",
                  fontSize: "0.875rem",
                  fontWeight: 500,
                  transition: "color 0.2s",
                  "&:hover": {
                    color: "primary.main",
                    textDecoration: "underline",
                  },
                }}
              >
                {crumb.label}
              </MuiLink>
            ) : (
              <Typography
                key={crumb.to}
                color="text.primary"
                sx={{
                  fontSize: "0.875rem",
                  fontWeight: 600,
                  color: "text.primary",
                }}
              >
                {crumb.label}
              </Typography>
            )
          )}
        </Breadcrumbs>
      </Box>

      {/* Ana Grid */}
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
          gap: 4,
          mb: 4,
        }}
      >
        {/* Sol: Büyük görsel ve galeri */}
        <Box>
          <Box
            sx={{
              position: "relative",
              borderRadius: 3,
              overflow: "hidden",
              boxShadow: "0 8px 32px rgba(149, 157, 165, 0.12)",
              mb: 2,
              bgcolor: "#fff",
            }}
          >
            <IconButton
              onClick={() =>
                setCurrentDetailIndex(
                  (prev) =>
                    (prev - 1 + galleryImages.length) % galleryImages.length
                )
              }
              sx={{
                position: "absolute",
                left: 8,
                top: "50%",
                transform: "translateY(-50%)",
                zIndex: 2,
                bgcolor: "#fff",
                opacity: 0.7,
                width: 40,
                height: 40,
                borderRadius: "50%",
                minWidth: 0,
                minHeight: 0,
                p: 0,
                boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
              }}
            >
              {"<"}
            </IconButton>
            <img
              src={
                galleryImages[currentDetailIndex]?.url ||
                "/placeholder-image.jpg"
              }
              alt={campaign?.name}
              style={{
                width: "100%",
                objectFit: "contain",
                maxHeight: 400,
                cursor: "pointer",
              }}
              onClick={handleOpenModal}
            />
            <IconButton
              onClick={() =>
                setCurrentDetailIndex(
                  (prev) => (prev + 1) % galleryImages.length
                )
              }
              sx={{
                position: "absolute",
                right: 8,
                top: "50%",
                transform: "translateY(-50%)",
                zIndex: 2,
                bgcolor: "#fff",
                opacity: 0.7,
                width: 40,
                height: 40,
                borderRadius: "50%",
                minWidth: 0,
                minHeight: 0,
                p: 0,
                boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
              }}
            >
              {">"}
            </IconButton>
          </Box>
          <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
            {galleryImages.map((img, idx) => (
              <img
                key={idx}
                src={img.url}
                alt="Detay"
                style={{
                  width: 60,
                  height: 60,
                  objectFit: "cover",
                  borderRadius: 8,
                  border:
                    idx === currentDetailIndex
                      ? "2px solid #1976d2"
                      : "1px solid #eee",
                  cursor: "pointer",
                  boxShadow:
                    idx === currentDetailIndex ? "0 0 8px #1976d2" : undefined,
                  transition: "border 0.2s, box-shadow 0.2s",
                }}
                onClick={() => {
                  setCurrentDetailIndex(idx);
                  handleOpenModal();
                }}
              />
            ))}
          </Box>
        </Box>

        {/* Sağ: Bilgi kutusu */}
        <Box
          sx={{
            borderRadius: 3,
            boxShadow: "0 8px 32px rgba(149, 157, 165, 0.12)",
            bgcolor: "#fff",
            p: 3,
            display: "flex",
            flexDirection: "column",
            height: "100%",
            gap: 2,
          }}
        >
          <Typography variant="h5" fontWeight={700}>
            {campaign?.name}
          </Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              cursor: campaign?.brandId || campaign?.brand ? "pointer" : "default",
            }}
            onClick={() => {
              if (campaign?.brand) {
                navigate(`/test/brands/${slugifyTurkish(campaign.brand)}`);
              } else if (campaign?.brandId) {
                navigate(`/test/brands/${campaign.brandId}`);
              }
            }}
          >
            <img
              src={getBrandLogoUrl(
                String(campaign?.brandId || ""),
                campaign?.logoUrl
              )}
              alt="Marka"
              style={{
                width: 40,
                height: 40,
                borderRadius: "50%",
                objectFit: "contain",
                background: "#fff",
                boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
              }}
            />
            <Typography variant="subtitle1" fontWeight={600}>
              {campaign?.brand}
            </Typography>
          </Box>
          <Typography variant="body1" color="text.secondary">
            {campaign?.description}
          </Typography>
          {/* Kalan süre ve bitiş tarihi */}
          <Typography variant="body2" color="primary" fontWeight={600}>
            {`Bitiş: ${formatDateWithTime(
              campaign?.endDate || ""
            )} (${getTimeRemaining(campaign?.endDate || "")})`}
          </Typography>
          {campaign && campaign.campaignUrl && (
            <MuiLink
              href={campaign.campaignUrl as string}
              target="_blank"
              rel="noopener noreferrer"
              variant="body2"
              sx={{ mt: 1, display: 'block', fontWeight: 'medium' }}
            >
              <FormattedMessage id="campaignDetails.viewDetailsLink" />
            </MuiLink>
          )}
          {/* İstatistikler */}
          <Box sx={{ display: "flex", gap: 2, alignItems: "center", mt: 1 }}>
            {/* Görüntülenme ve ilgilenen sayıları kaldırıldı */}
          </Box>
          {campaign?.discount && campaign.discount !== "%0" && (
            <Typography variant="h6" color="success.main" fontWeight={700}>
              {campaign?.discount}
            </Typography>
          )}
          <Box sx={{ flexGrow: 1 }} />
          <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
            <IconButton onClick={handleOpenShareDialog} color="primary" id="share-button">
              <ShareIcon />
            </IconButton>
            <Dialog open={isShareDialogOpen} onClose={handleCloseShareDialog} PaperProps={{ sx: { borderRadius: 2, minWidth: '300px' } }}>
              <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
                {intl.formatMessage({ id: 'campaignDetails.share.title', defaultMessage: 'Share Campaign' })}
              </DialogTitle>
              <DialogContent sx={{ pt: '0 !important' }}>
                <List>
                  {typeof navigator.share === 'function' && (
                    <ListItemButton onClick={handleGenericShare} sx={{ borderRadius: 2, mb: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: '40px' }}><ShareIcon /></ListItemIcon>
                      <ListItemText primary={intl.formatMessage({ id: 'campaignDetails.share.native', defaultMessage: 'Share...' })} />
                    </ListItemButton>
                  )}
                  <ListItemButton onClick={shareViaWhatsApp} sx={{ borderRadius: 2, mb: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: '40px' }}><WhatsAppIcon /></ListItemIcon>
                    <ListItemText primary={intl.formatMessage({ id: 'campaignDetails.share.whatsapp', defaultMessage: 'WhatsApp' })} />
                  </ListItemButton>
                  <ListItemButton onClick={shareViaEmail} sx={{ borderRadius: 2, mb: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: '40px' }}><MailIcon /></ListItemIcon>
                    <ListItemText primary={intl.formatMessage({ id: 'campaignDetails.share.email', defaultMessage: 'Email' })} />
                  </ListItemButton>
                  <ListItemButton onClick={copyLinkToClipboard} sx={{ borderRadius: 2, mb: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: '40px' }}><ContentCopyIcon /></ListItemIcon>
                    <ListItemText primary={intl.formatMessage({ id: 'campaignDetails.share.copyLink', defaultMessage: 'Copy Link' })} />
                  </ListItemButton>
                  <ListItemButton onClick={shareViaInstagram} sx={{ borderRadius: 2, mb: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: '40px' }}><InstagramIcon /></ListItemIcon>
                    <ListItemText primary={intl.formatMessage({ id: 'campaignDetails.share.instagramDM', defaultMessage: 'Instagram DM' })} />
                  </ListItemButton>
                  <ListItemButton onClick={shareViaTwitterDM} sx={{ borderRadius: 2, mb: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: '40px' }}><TwitterIcon /></ListItemIcon>
                    <ListItemText primary={intl.formatMessage({ id: 'campaignDetails.share.twitterDM', defaultMessage: 'Twitter DM' })} />
                  </ListItemButton>
                  <ListItemButton onClick={shareAsTweet} sx={{ borderRadius: 2 }}>
                    <ListItemIcon sx={{ minWidth: '40px' }}><TwitterIcon /></ListItemIcon>
                    <ListItemText primary={intl.formatMessage({ id: 'campaignDetails.share.tweet', defaultMessage: 'Share as Tweet' })} />
                  </ListItemButton>
                </List>
              </DialogContent>
            </Dialog>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <ViewIcon fontSize="small" color="primary" sx={{ mr: 0.5 }} />
              <Typography
                variant="body2"
                color="text.secondary"
                fontWeight={500}
              >
                <FormattedMessage
                  id="campaign.stats.viewsDetailed"
                  values={{ count: views24h.toLocaleString() }}
                />
              </Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <PeopleIcon fontSize="small" color="primary" sx={{ mr: 0.5 }} />
              <Typography
                variant="body2"
                color="text.secondary"
                fontWeight={500}
              >
                <FormattedMessage
                  id="campaign.stats.interestedDetailed"
                  values={{ count: interested24h.toLocaleString() }}
                />
              </Typography>
            </Box>
          </Box>
          {(() => {
            if (checkingExistingForm) {
              return (
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  disabled
                  sx={{ width: "100%", fontWeight: 600, borderRadius: 2 }}
                >
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Kontrol ediliyor...
                </Button>
              );
            }

            if (hasExistingForm) {
              return (
                <Box
                  sx={{
                    width: "100%",
                    p: 2,
                    borderRadius: 2,
                    bgcolor: theme.palette.grey[300],
                    border: "1px solid",
                    borderColor: theme.palette.grey[500],
                    textAlign: "center",
                    ...(isDarkMode && { bgcolor: theme.palette.grey[800], borderColor: theme.palette.grey[700] }),
                  }}
                >
                  <Typography variant="body1" fontWeight={600} color="text.primary">
                    Bu formu zaten doldurdunuz
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    Bu kampanya için daha önce teklif aldınız
                  </Typography>
                </Box>
              );
            }

            return (
              <Button
                variant="contained"
                color="primary"
                size="large"
                sx={{ width: "100%", fontWeight: 600, borderRadius: 2 }}
                onClick={handleApplyClick}
              >
                <FormattedMessage id="campaign.getQuoteButton" />
              </Button>
            );
          })()}
        </Box>
      </Box>

      {/* Kampanya Detayları ve Açıklama Sekmeleri */}
      {(campaign?.description ||
        (campaign?.details && Object.keys(campaign.details).length > 0)) && (
        <Box sx={{ mt: 4, mb: 4 }}>
          <Tabs
            value={detailsTab}
            onChange={handleDetailsTabChange}
            aria-label="Kampanya detay sekmeleri"
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              mb: 2,
              "& .MuiTab-root": {
                fontWeight: 600,
                fontSize: "1rem",
                textTransform: "none",
                minWidth: 120,
              },
            }}
          >
            <Tab
              label={intl.formatMessage({ id: "campaign.descriptionTab" })}
            />
            <Tab label={intl.formatMessage({ id: "campaign.detailsTab" })} />
          </Tabs>
          {detailsTab === 0 && (
            <Box
              sx={{
                p: 2,
                bgcolor: "#fff",
                borderRadius: 3,
                boxShadow: "0 8px 32px rgba(149, 157, 165, 0.08)",
              }}
            >
              <Typography variant="h5" fontWeight={700} sx={{ mb: 2 }}>
                {campaign?.title ? String(campaign.title) : ""}
              </Typography>
              <Typography variant="body1" color="text.primary">
                {campaign?.description
                  ? String(campaign.description)
                  : intl.formatMessage({ id: "campaign.noDescription" })}
              </Typography>
            </Box>
          )}
          {detailsTab === 1 &&
            campaign?.details &&
            Object.keys(campaign.details).length > 0 && (
              <Box
                sx={{
                  mt: 0,
                  borderRadius: 3,
                  boxShadow: "0 8px 32px rgba(149, 157, 165, 0.12)",
                  bgcolor: "#fff",
                  p: 3,
                  overflow: "hidden",
                }}
              >
                <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                  {intl.formatMessage({ id: "campaign.detailsTab" })}
                </Typography>
                <Box
                  sx={{
                    width: "100%",
                    overflowX: "auto",
                    "&::-webkit-scrollbar": {
                      height: "8px",
                    },
                    "&::-webkit-scrollbar-track": {
                      background: isDarkMode
                        ? "rgba(255,255,255,0.1)"
                        : "rgba(0,0,0,0.1)",
                      borderRadius: "4px",
                    },
                    "&::-webkit-scrollbar-thumb": {
                      background: isDarkMode
                        ? "rgba(255,255,255,0.2)"
                        : "rgba(0,0,0,0.2)",
                      borderRadius: "4px",
                      "&:hover": {
                        background: isDarkMode
                          ? "rgba(255,255,255,0.3)"
                          : "rgba(0,0,0,0.3)",
                      },
                    },
                  }}
                >
                  <Box
                    component="table"
                    sx={{
                      width: "100%",
                      borderCollapse: "collapse",
                      "& th, & td": {
                        border: "1px solid",
                        borderColor: isDarkMode
                          ? "rgba(255,255,255,0.1)"
                          : "rgba(0,0,0,0.1)",
                        p: 2,
                        textAlign: "left",
                      },
                      "& th": {
                        bgcolor: isDarkMode
                          ? "rgba(255,255,255,0.05)"
                          : "rgba(0,0,0,0.02)",
                        fontWeight: 600,
                        color: "text.primary",
                      },
                      "& tr:nth-of-type(even)": {
                        bgcolor: isDarkMode
                          ? "rgba(255,255,255,0.02)"
                          : "rgba(0,0,0,0.01)",
                      },
                      "& tr:hover": {
                        bgcolor: isDarkMode
                          ? "rgba(255,255,255,0.05)"
                          : "rgba(0,0,0,0.02)",
                      },
                    }}
                  >
                    <Box component="thead">
                      <Box component="tr">
                        <Box component="th" sx={{ width: "40%" }}>
                          {intl.formatMessage({
                            id: "campaign.details.feature",
                          })}
                        </Box>
                        <Box component="th" sx={{ width: "60%" }}>
                          {intl.formatMessage({ id: "campaign.details.value" })}
                        </Box>
                      </Box>
                    </Box>
                    <Box component="tbody">
                      {Object.entries(campaign.details).map(([key, field]) => {
                        if (!field || !field.label) return null;

                        let value = field.value;
                        if (Array.isArray(value)) {
                          value = value.join(", ");
                        }

                        return (
                          <Box component="tr" key={key}>
                            <Box component="td">
                              <Typography
                                variant="subtitle2"
                                color="text.secondary"
                              >
                                {field.label}
                              </Typography>
                            </Box>
                            <Box component="td">
                              {field.type === "checkbox" ? (
                                <Typography
                                  variant="body2"
                                  color={value ? "success.main" : "error.main"}
                                  fontWeight={500}
                                >
                                  {value ? "Evet" : "Hayır"}
                                </Typography>
                              ) : field.type === "radio" ? (
                                <Typography variant="body2" fontWeight={500}>
                                  {value}
                                </Typography>
                              ) : (
                                <Typography variant="body2" fontWeight={500}>
                                  {value}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        );
                      })}
                    </Box>
                  </Box>
                </Box>
              </Box>
            )}
        </Box>
      )}

      {relatedCampaigns.length > 0 && (
        <Box sx={{ mt: 6, mb: 4 }}>
          <Typography variant="h5" fontWeight={600} sx={{ mb: 3 }}>
            {intl.formatMessage(
              { id: "campaign.relatedCampaignsWithCategory" },
              { category: categoryName }
            )}
          </Typography>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: {
                xs: "1fr 1fr",
                sm: "1fr 1fr 1fr",
                md: "1fr 1fr 1fr 1fr",
              },
              gap: 2,
            }}
          >
            {relatedCampaigns.map((related) => (
              <Box
                key={related.id}
                sx={{
                  borderRadius: 2,
                  boxShadow: "0 4px 16px rgba(149,157,165,0.10)",
                  bgcolor: "#fff",
                  overflow: "hidden",
                  cursor: "pointer",
                  transition: "all 0.2s",
                  "&:hover": {
                    boxShadow: "0 8px 32px rgba(149,157,165,0.18)",
                    transform: "translateY(-4px)",
                  },
                }}
                onClick={() => navigate(isTestMode ? `/test/campaigns/${slugifyTurkish(related.name)}` : `/campaigns/${slugifyTurkish(related.name)}`)}
              >
                <img
                  src={`https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${related.id}`}
                  alt={related.name}
                  style={{ width: "100%", height: 140, objectFit: "cover" }}
                />
                <Box sx={{ p: 2 }}>
                  <Typography variant="subtitle1" fontWeight={600}>
                    {related.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {related.brand}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mt: 1 }}
                  >
                    {related.description?.slice(0, 60)}...
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      )}

      <QuoteForm
        open={quoteFormOpen}
        onClose={handleQuoteFormClose}
        campaignDetails={{
          id: campaign.id,
          title: campaign.name,
          category: campaign.category,
          brand: campaign.brand,
          imageUrl: campaign.imageUrl,
        }}
      />

      <Snackbar
        open={showShareAlert}
        autoHideDuration={3000}
        onClose={() => setShowShareAlert(false)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setShowShareAlert(false)}
          severity="success"
          sx={{
            width: "100%",
            borderRadius: 3,
            backdropFilter: "blur(10px)",
            background: isDarkMode
              ? alpha(theme.palette.success.main, 0.9)
              : theme.palette.success.light,
            boxShadow: `0 4px 12px ${alpha(theme.palette.success.main, 0.3)}`,
          }}
        >
          {intl.formatMessage({ id: "campaign.shareCopied" })}
        </Alert>
      </Snackbar>

      {/* New Snackbar for Instagram Info */}
      <Snackbar
        open={showInstagramInfoSnackbar}
        autoHideDuration={6000} // Longer duration for instructional message
        onClose={() => setShowInstagramInfoSnackbar(false)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setShowInstagramInfoSnackbar(false)}
          severity="info" // Using info severity for instructions
          sx={{
            width: "100%",
            borderRadius: 3,
            backdropFilter: "blur(10px)",
            background: isDarkMode
              ? alpha(theme.palette.info.main, 0.9)
              : theme.palette.info.light,
            boxShadow: `0 4px 12px ${alpha(theme.palette.info.main, 0.3)}`,
            color: theme.palette.info.contrastText, // Ensure text is readable
          }}
        >
          {instagramSnackbarMessage}
        </Alert>
      </Snackbar>

      {/* Vitrin Görseli Modal */}
      <Dialog
        open={isShowcaseModal}
        onClose={handleCloseShowcaseModal}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor: isDarkMode ? "#1a1a1a" : "#fff",
            borderRadius: 4,
            overflow: "hidden",
          },
        }}
      >
        <Box
          sx={{
            position: "relative",
            bgcolor: isDarkMode ? "#1a1a1a" : "#fff",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            minHeight: "80vh",
            p: 2,
          }}
        >
          <IconButton
            aria-label="close"
            onClick={handleCloseShowcaseModal}
            sx={{
              position: "absolute",
              right: 16,
              top: 16,
              color: isDarkMode ? "grey.400" : "grey.600",
              bgcolor: isDarkMode ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.1)",
              "&:hover": {
                bgcolor: isDarkMode
                  ? "rgba(255,255,255,0.2)"
                  : "rgba(0,0,0,0.2)",
              },
              zIndex: 2,
            }}
          >
            <CloseIcon />
          </IconButton>

          {showcaseImage && (
            <img
              src={showcaseImage.url}
              alt={campaign.name}
              style={{
                width: "100%",
                maxHeight: "80vh",
                objectFit: "contain",
                display: "block",
                margin: "0 auto",
                borderRadius: 8,
                transition: "all 0.3s ease",
              }}
            />
          )}
        </Box>
      </Dialog>

      {/* Tam ekran modal */}
      <Dialog
        open={modalOpen}
        onClose={handleCloseModal}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor: isDarkMode ? "#1a1a1a" : "#fff",
            borderRadius: 4,
            overflow: "hidden",
          },
        }}
      >
        <Box
          sx={{
            position: "relative",
            bgcolor: isDarkMode ? "#1a1a1a" : "#fff",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            minHeight: "80vh",
            p: 2,
          }}
        >
          <IconButton
            aria-label="close"
            onClick={handleCloseModal}
            sx={{
              position: "absolute",
              right: 16,
              top: 16,
              color: isDarkMode ? "grey.400" : "grey.600",
              bgcolor: isDarkMode ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.1)",
              "&:hover": {
                bgcolor: isDarkMode
                  ? "rgba(255,255,255,0.2)"
                  : "rgba(0,0,0,0.2)",
              },
              zIndex: 2,
            }}
          >
            <CloseIcon />
          </IconButton>

          <IconButton
            onClick={() =>
              setCurrentDetailIndex(
                (prev) =>
                  (prev - 1 + galleryImages.length) % galleryImages.length
              )
            }
            sx={{
              position: "absolute",
              left: 16,
              top: "50%",
              transform: "translateY(-50%)",
              color: isDarkMode ? "grey.400" : "grey.600",
              bgcolor: isDarkMode ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.1)",
              "&:hover": {
                bgcolor: isDarkMode
                  ? "rgba(255,255,255,0.2)"
                  : "rgba(0,0,0,0.2)",
              },
              zIndex: 2,
            }}
          >
            <NavigateNextIcon sx={{ transform: "rotate(180deg)" }} />
          </IconButton>

          <Box
            sx={{
              width: "100%",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              flex: 1,
              position: "relative",
            }}
          >
            <img
              src={galleryImages[currentDetailIndex]?.url}
              alt="Büyük görsel"
              style={{
                width: "100%",
                maxHeight: "70vh",
                objectFit: "contain",
                display: "block",
                margin: "0 auto",
                borderRadius: 8,
                transition: "all 0.3s ease",
              }}
              onLoad={handleImageLoad}
            />

            {!imageLoaded && (
              <Box
                sx={{
                  position: "absolute",
                  top: "50%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                }}
              >
                <CircularProgress />
              </Box>
            )}
          </Box>

          <IconButton
            onClick={() =>
              setCurrentDetailIndex((prev) => (prev + 1) % galleryImages.length)
            }
            sx={{
              position: "absolute",
              right: 16,
              top: "50%",
              transform: "translateY(-50%)",
              color: isDarkMode ? "grey.400" : "grey.600",
              bgcolor: isDarkMode ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.1)",
              "&:hover": {
                bgcolor: isDarkMode
                  ? "rgba(255,255,255,0.2)"
                  : "rgba(0,0,0,0.2)",
              },
              zIndex: 2,
            }}
          >
            <NavigateNextIcon />
          </IconButton>

          {/* Küçük fotoğraflar barı */}
          <Box
            sx={{
              display: "flex",
              gap: 1,
              mt: 2,
              p: 2,
              width: "100%",
              overflowX: "auto",
              "&::-webkit-scrollbar": {
                height: "8px",
              },
              "&::-webkit-scrollbar-track": {
                background: isDarkMode
                  ? "rgba(255,255,255,0.1)"
                  : "rgba(0,0,0,0.1)",
                borderRadius: "4px",
              },
              "&::-webkit-scrollbar-thumb": {
                background: isDarkMode
                  ? "rgba(255,255,255,0.2)"
                  : "rgba(0,0,0,0.2)",
                borderRadius: "4px",
                "&:hover": {
                  background: isDarkMode
                    ? "rgba(255,255,255,0.3)"
                    : "rgba(0,0,0,0.3)",
                },
              },
            }}
          >
            {galleryImages.map((img, idx) => (
              <img
                key={idx}
                src={img.url}
                alt="Detay"
                style={{
                  width: 80,
                  height: 80,
                  objectFit: "cover",
                  borderRadius: 8,
                  border:
                    idx === currentDetailIndex
                      ? "2px solid #1976d2"
                      : "1px solid #eee",
                  cursor: "pointer",
                  boxShadow:
                    idx === currentDetailIndex ? "0 0 8px #1976d2" : undefined,
                  transition: "all 0.2s ease",
                  flexShrink: 0,
                }}
                onClick={() => setCurrentDetailIndex(idx)}
              />
            ))}
          </Box>
        </Box>
      </Dialog>

      {/* Süresi Dolmuş Kampanya Mesajı Modal */}
      <Dialog
        open={showEndedMessage}
        onClose={() => setShowEndedMessage(false)}
        PaperProps={{
          sx: {
            borderRadius: 3,
            p: 2,
            textAlign: 'center',
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" fontWeight={600} sx={{ mb: 2 }}>
            Kampanya Süresi Dolmuştur
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Bu kampanyanın başvuru tarihi sona ermiştir.
          </Typography>
          <Button
            variant="contained"
            onClick={() => setShowEndedMessage(false)}
            autoFocus
          >
            Tamam
          </Button>
        </Box>
      </Dialog>
    </Container>
  );
};

export default CampaignDetail;
