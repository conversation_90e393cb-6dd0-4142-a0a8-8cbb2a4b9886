import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import CssBaseline from '@mui/material/CssBaseline';
import Layout from './components/layout/Layout';
import ScrollToTop from './components/layout/ScrollToTop';
import { ThemeProvider } from './store/ThemeContext';
import { LanguageProvider } from './i18n/LanguageContext';
import { CategoryProvider } from './contexts/CategoryContext';
import { AuthProvider } from './contexts/AuthContext';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { tr } from 'date-fns/locale';
import ProtectedRoute from './components/common/ProtectedRoute';
import CookieConsentBanner from './components/common/CookieConsentBanner';

import { setDefaultConsent, loadGTM, hasUserConsented, getCurrentPreferences, applyCookiePreferences, handleImplicitConsent } from './utils/cookieConsentUtils';

// Admin Pages
import DashboardPage from './pages/admin/DashboardPage';
import CategoriesPage from './pages/admin/CategoriesPage';
import CampaignsPage from './pages/admin/CampaignsPage';
import BrandsPage from './pages/admin/BrandsPage';
import CustomersPage from './pages/admin/CustomersPage';
import FormsPage from './pages/admin/FormsPage';
import BrandCategoriesPage from './pages/admin/BrandCategoriesPage';
import BrandCampaignsPage from './pages/admin/BrandCampaignsPage';
import CustomerCampaignsPage from './pages/admin/CustomerCampaignsPage';
import FormCampaignsPage from './pages/admin/FormCampaignsPage';
import CampaignUrlsPage from './pages/admin/CampaignUrlsPage';
import JobsPage from './pages/admin/JobsPage';
import CampaignDetailsPage from './pages/admin/CampaignDetailsPage';
import CampaignImagesPage from './pages/admin/CampaignImagesPage';
import CampaignFormsManagementPage from './pages/admin/CampaignFormsManagementPage';
import SubscribedUsersPage from './pages/admin/SubscribedUsersPage';

import Chatbot from './components/features/chatbot/Chatbot';

const App: React.FC = () => {
  useEffect(() => {
    // Uygulama başlatıldığında GTM'i başlat
    setDefaultConsent(); // Önce varsayılan consent'i ayarla
    loadGTM(); // Sonra GTM'i yükle

    // Eğer kullanıcı daha önce consent vermişse, tercihlerini uygula
    if (hasUserConsented()) {
      const preferences = getCurrentPreferences();
      applyCookiePreferences(preferences);
    } else {
      // Kullanıcı consent vermemişse, örtülü consent'i başlat
      handleImplicitConsent();
    }
  }, []);

  return (
    <ThemeProvider>
      <LanguageProvider>
        <CategoryProvider>
          <AuthProvider>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={tr}>
              <CssBaseline />
              <Routes>
                <Route path="/admin" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <Navigate to="/admin/dashboard" replace />
                  </ProtectedRoute>
                } />
                <Route path="/admin/dashboard" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <DashboardPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/categories" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <CategoriesPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/campaigns" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <CampaignsPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/brands" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <BrandsPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/customers" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <CustomersPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/forms" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <FormsPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/brand-categories" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <BrandCategoriesPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/brand-campaigns" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <BrandCampaignsPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/customer-campaigns" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <CustomerCampaignsPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/form-campaigns" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <FormCampaignsPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/campaign-urls" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <CampaignUrlsPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/jobs" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <JobsPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/campaign-details" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <CampaignDetailsPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/campaign-images" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <CampaignImagesPage />
                  </ProtectedRoute>
                } />

                <Route path="/admin/campaign-forms-management" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <CampaignFormsManagementPage />
                  </ProtectedRoute>
                } />
                <Route path="/admin/subscribed-users" element={
                  <ProtectedRoute requiredRole="ADMIN">
                    <SubscribedUsersPage />
                  </ProtectedRoute>
                } />
                <Route path="/*" element={<Layout />} />
              </Routes>
              <ScrollToTop />
              <CookieConsentBanner
                onConsentGiven={() => {
                  // Cookie consent verildiğinde GTM'e bildir
                  const preferences = getCurrentPreferences();
                  applyCookiePreferences(preferences);
                }}
              />
              <Chatbot />
            </LocalizationProvider>
          </AuthProvider>
        </CategoryProvider>
      </LanguageProvider>
    </ThemeProvider>
  );
};

export default App;