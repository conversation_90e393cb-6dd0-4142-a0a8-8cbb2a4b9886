/**
 * Campaign tracking utilities using cookies
 * Tracks campaign clicks and provides popular campaigns functionality
 */

interface CampaignClickData {
  [campaignId: string]: {
    count: number;
    lastClicked: string;
    campaignName: string;
    campaignCategory?: string;
  };
}

const COOKIE_NAME = 'campaign_clicks';
const COOKIE_EXPIRY_DAYS = 365; // 1 year

/**
 * Get campaign click data from cookies
 */
export const getCampaignClickData = (): CampaignClickData => {
  try {
    const cookieValue = getCookie(COOKIE_NAME);
    if (!cookieValue) {
      return {};
    }
    return JSON.parse(decodeURIComponent(cookieValue));
  } catch (error) {
    console.error('Error parsing campaign click data:', error);
    return {};
  }
};

/**
 * Save campaign click data to cookies
 */
export const saveCampaignClickData = (data: CampaignClickData): void => {
  try {
    const encodedData = encodeURIComponent(JSON.stringify(data));
    setCookie(COOKIE_NAME, encodedData, COOKIE_EXPIRY_DAYS);
  } catch (error) {
    console.error('Error saving campaign click data:', error);
  }
};

/**
 * Track a campaign click
 */
export const trackCampaignClick = (
  campaignId: string,
  campaignName: string,
  campaignCategory?: string
): void => {
  try {
    const currentData = getCampaignClickData();
    const now = new Date().toISOString();

    currentData[campaignId] = {
      count: (currentData[campaignId]?.count || 0) + 1,
      lastClicked: now,
      campaignName,
      campaignCategory,
    };

    saveCampaignClickData(currentData);
  } catch (error) {
    console.error('Error tracking campaign click:', error);
  }
};

/**
 * Get popular campaigns sorted by click count
 */
export const getPopularCampaigns = (limit: number = 10): Array<{
  campaignId: string;
  campaignName: string;
  campaignCategory?: string;
  clickCount: number;
  lastClicked: string;
}> => {
  try {
    const clickData = getCampaignClickData();
    
    return Object.entries(clickData)
      .map(([campaignId, data]) => ({
        campaignId,
        campaignName: data.campaignName,
        campaignCategory: data.campaignCategory,
        clickCount: data.count,
        lastClicked: data.lastClicked,
      }))
      .sort((a, b) => b.clickCount - a.clickCount)
      .slice(0, limit);
  } catch (error) {
    console.error('Error getting popular campaigns:', error);
    return [];
  }
};

/**
 * Get campaign click count for a specific campaign
 */
export const getCampaignClickCount = (campaignId: string): number => {
  try {
    const clickData = getCampaignClickData();
    return clickData[campaignId]?.count || 0;
  } catch (error) {
    console.error('Error getting campaign click count:', error);
    return 0;
  }
};

/**
 * Clear all campaign tracking data
 */
export const clearCampaignTrackingData = (): void => {
  try {
    deleteCookie(COOKIE_NAME);
  } catch (error) {
    console.error('Error clearing campaign tracking data:', error);
  }
};

/**
 * Get campaigns clicked in the last N days
 */
export const getRecentlyClickedCampaigns = (days: number = 7): Array<{
  campaignId: string;
  campaignName: string;
  campaignCategory?: string;
  clickCount: number;
  lastClicked: string;
}> => {
  try {
    const clickData = getCampaignClickData();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    return Object.entries(clickData)
      .filter(([, data]) => new Date(data.lastClicked) >= cutoffDate)
      .map(([campaignId, data]) => ({
        campaignId,
        campaignName: data.campaignName,
        campaignCategory: data.campaignCategory,
        clickCount: data.count,
        lastClicked: data.lastClicked,
      }))
      .sort((a, b) => new Date(b.lastClicked).getTime() - new Date(a.lastClicked).getTime());
  } catch (error) {
    console.error('Error getting recently clicked campaigns:', error);
    return [];
  }
};

// Cookie utility functions
function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null;
  
  const nameEQ = name + '=';
  const ca = document.cookie.split(';');
  
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
}

function setCookie(name: string, value: string, days: number): void {
  if (typeof document === 'undefined') return;
  
  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
  
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
}

function deleteCookie(name: string): void {
  if (typeof document === 'undefined') return;
  
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
}
